// Actual Generic Game Home Screen Component
import React from 'react';
import Config<PERSON><PERSON> from '../../../Config/ConfigAPI';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';

// Generic Game Home Screen Configuration Interface
export interface GameConfig {
  // Basic Info
  gameId: string;
  gameName: string;

  // Assets
  backgroundImage: any;
  milestoneImages: {
    completed: any;
    inProgress: any;
    locked: any;
  };
  birdImage?: any;
  footerIcons: {
    coin: any;
    rank: any;
    level: any;
    help: any;
  };

  // Content
  modalContent: string;
  helpText: string;

  // Navigation
  startGameScreen: string;
  startGameComponent?: React.ComponentType<any>;

  // Styling
  colors: {
    primary: string;
    footer: string;
    text: string;
  };

  // Game-specific Logic
  milestonePositions?: Array<{
    id: number;
    top: number;
    left: number;
  }>;

  // Custom hooks (optional)
  useSVGPath?: (params: any) => any;
  usePathTrail?: (params: any) => any;
}

// Default configurations for different games
export const GAME_CONFIGS: Record<string, GameConfig> = {
  // Game ALTP
  cf86bc33ef03447fa744eea2bbf31cfc: {
    gameId: ConfigAPI.gameALTP,
    gameName: 'Ai là triệu phú',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'),
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Vượt qua 15 câu \n với 4 sự trợ giúp',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Trả lời đúng các câu hỏi để mở khóa các chặng tiếp theo.',
    startGameScreen: 'StartALTP',
    colors: {
      primary: '#112164',
      footer: '#FF5A5F',
      text: '#fff',
    },
  },

  '7eb45b0edc6247c3bde6bbb15547dfda': {
    gameId: ConfigAPI.gameSakuTB,
    gameName: 'Saku Tìm Bạn',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP assets
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Ghép đôi các từ \n trong thời gian quy định',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Ghép đôi các từ đúng để hoàn thành chặng.',
    startGameScreen: 'StartSakuTB',
    colors: {
      primary: '#2E7D32',
      footer: '#4CAF50',
      text: '#fff',
    },
  },

  '1d56852db9964d9a878a1d9d5f872cb7': {
    gameId: '1d56852db9964d9a878a1d9d5f872cb7',
    gameName: 'Đuổi Hình Bắt Chữ',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP assets
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Đoán từ từ hình ảnh \n trong thời gian quy định',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Đoán từ đúng từ hình ảnh để hoàn thành chặng.',
    startGameScreen: 'StartDHBC',
    colors: {
      primary: '#7B1FA2',
      footer: '#9C27B0',
      text: '#fff',
    },
  },
  '1b1804be1c6049c2876d1626794fa7a0': {
    gameId: '1b1804be1c6049c2876d1626794fa7a0',
    gameName: 'Mảnh ghép hoàn hảo',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP assets
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Đoán từ từ hình ảnh \n trong thời gian quy định',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Đoán từ đúng từ hình ảnh để hoàn thành chặng.',
    startGameScreen: 'StartMGHH',
    colors: {
      primary: '#7B1FA2',
      footer: '#9C27B0',
      text: '#fff',
    },
  },
  '769bce29753d4fa48314325c4bc7ebb0': {
    gameId: '769bce29753d4fa48314325c4bc7ebb0',
    gameName: 'Saku Luyện công',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP assets
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Đoán từ từ hình ảnh \n trong thời gian quy định',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Đoán từ đúng từ hình ảnh để hoàn thành chặng.',
    startGameScreen: 'StartSakuLC',
    colors: {
      primary: '#7B1FA2',
      footer: '#9C27B0',
      text: '#fff',
    },
  },

  '19d8c9d61ae74f968416b28fcf8e93c3': {
    gameId: ConfigAPI.gameSKXT,
    gameName: 'Saku Xây Tổ',
    backgroundImage: require('../sakuxayto/assets/background.png'), // Tạm dùng ALTP assets
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Kéo và thả các từ \n trong thời gian quy định',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Sắp xếp đúng câu theo đề bài để hoàn thành chặng.',
    startGameScreen: 'StartSakuXT',
    colors: {
      primary: '#7B1FA2',
      footer: '#9C27B0',
      text: '#fff',
    },
  },
  '05ac80f4e3b54615afb06d49f5140ade': {
    gameId: ConfigAPI.gameSakusanmoi,
    gameName: 'Saku Săn Mồi',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP assets
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Đoán từ từ hình ảnh \n trong thời gian quy định',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Đoán từ đúng từ hình ảnh để hoàn thành chặng.',
    startGameScreen: 'StartSakuSanMoi',
    colors: {
      primary: '#7B1FA2',
      footer: '#9C27B0',
      text: '#fff',
    },
  },




  // Có thể thêm các game khác...
};

// Props interface for Generic Home Screen
export interface GenericGameHomeScreenProps {
  gameConfig: GameConfig;
  // Override specific components if needed
  HeaderComponent?: React.ComponentType<any>;
  FooterComponent?: React.ComponentType<any>;
  MilestoneComponent?: React.ComponentType<any>;
  StartGameModalComponent?: React.ComponentType<any>;

  // Custom handlers
  onMilestonePress?: (
    status: string,
    number: number,
    levelName?: string,
  ) => void;
  onLevelSelect?: (level: any) => void;

  // Additional props
  customStyles?: any;
  customHooks?: {
    useSVGPath?: (params: any) => any;
    usePathTrail?: (params: any) => any;
  };
}

// Utility function to get game config
export const getGameConfig = (gameId: string): GameConfig => {
  const config = GAME_CONFIGS[gameId];
  if (!config) {
    showSnackbar({
      message: 'Trò chơi này đang được phát triển',
      status: ComponentStatus.INFOR,
    });
    return getGameConfig('ALTP');
    // throw new Error(`Game config not found for gameId: ${gameId}`);
  }
  return config;
};

// Utility function to merge custom config with default
export const mergeGameConfig = (
  gameId: string,
  customConfig: Partial<GameConfig>,
): GameConfig => {
  const defaultConfig = getGameConfig(gameId);
  return {
    ...defaultConfig,
    ...customConfig,
    // Deep merge for nested objects
    milestoneImages: {
      ...defaultConfig.milestoneImages,
      ...customConfig.milestoneImages,
    },
    footerIcons: {
      ...defaultConfig.footerIcons,
      ...customConfig.footerIcons,
    },
    colors: {
      ...defaultConfig.colors,
      ...customConfig.colors,
    },
  };
};

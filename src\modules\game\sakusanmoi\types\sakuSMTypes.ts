// TypeScript interfaces cho SakuSM Game

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T[];
  total?: number;
}

// Data từ bảng GameConfig
export interface SakuSMGameConfigAPI {
  Id: string;
  GameId: string;
  Score: number;        // Điểm trên mỗi mạng
  LifeCount: number;    // Số mạng chơi
  Time: number;         // Thời gian chơi (giây)
  Bonus: number;        // Điểm bonus khi hoàn thành không mất mạng
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
}

// Data từ bảng GameQuestion
export interface SakuSMGameQuestionAPI {
  Id: string;
  GameId: string;
  Stage: number;
  Name: string;         // Nội dung câu hỏi
  Suggest?: string;     // Nội dung gợi ý (hint)
  Purpose: string;      // CompetenceId
  Sort?: number;
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
}

// Data từ bảng GameAnswer
export interface SakuSMGameAnswerAPI {
  Id: string;
  GameQuestionId: string;
  Name: string;         // Nội dung đáp án
  IsResult: boolean;    // true = đáp án đúng, false = đáp án sai
  Sort?: number;
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
}

// Interface cho đáp án đã transform để sử dụng trong game
export interface SakuSMAnswer {
  id: string;
  text: string;
  isTrue: boolean;
}

// Interface cho câu hỏi đã transform để sử dụng trong game
export interface SakuSMQuestion {
  id: string;
  question: string;
  hint: string;         // Từ field Suggest, có thể rỗng
  answers: SakuSMAnswer[];
}

// Interface cho game config đã transform
export interface SakuSMGameConfig {
  gameId: string;
  scorePerLife: number;
  maxLives: number;
  timeLimit: number;
  bonusScore: number;
  isActive: boolean;
}

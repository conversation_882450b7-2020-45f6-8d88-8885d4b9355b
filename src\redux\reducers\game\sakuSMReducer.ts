import {createSlice} from '@reduxjs/toolkit';

export interface SakuSmAnswer {
  id: number;
  text: string;
  isTrue: boolean;
}

interface Question {
  id: number;
  question: string;
  hint: string;
  answers: SakuSmAnswer[];
}

const data: Question[] = [
  {
    id: 1,
    question:
      'Tân Tổng thống Ukraine Volodymyr <PERSON> làm nghề gì trước khi nhậm chức?',
    hint: 'Nghề này thường đóng trong các bộ phim hoặc vở kịch hài hước',
    answers: [
      {
        id: 1,
        text: 'Võ sĩ quyền anh',
        isTrue: false,
      },
      {
        id: 2,
        text: 'Diễn viên hài',
        isTrue: true,
      },
      {
        id: 3,
        text: '<PERSON><PERSON><PERSON> sĩ phẫu thuật',
        isTrue: false,
      },
      {
        id: 4,
        text: 'Doanh nhân',
        isTrue: false,
      },
    ],
  },
  {
    id: 2,
    question: '<PERSON><PERSON><PERSON> là tên một loại đồ chơi dân gian của trẻ em?',
    hint: 'là một loại đồ chơi trẻ em dân gian của Vi<PERSON>, có thể ăn được',
    answers: [
      {
        id: 1,
        text: 'Tò he',
        isTrue: true,
      },
      {
        id: 2,
        text: 'Tò mò',
        isTrue: false,
      },
      {
        id: 3,
        text: 'Tò vò',
        isTrue: false,
      },
      {
        id: 4,
        text: 'Tến tò',
        isTrue: false,
      },
    ],
  },
  {
    id: 3,
    question: 'Có câu: "Mật ngọt chết ..." gì?',
    hint: 'là một loại đồ chơi trẻ em dân gian của Việt Nam, có thể ăn được',
    answers: [
      {
        id: 1,
        text: 'Đàn gà',
        isTrue: false,
      },
      {
        id: 2,
        text: 'Ruồi',
        isTrue: true,
      },
      {
        id: 3,
        text: 'Đàn bò',
        isTrue: false,
      },
      {
        id: 4,
        text: 'Đàn chó',
        isTrue: false,
      },
    ],
  },
  {
    id: 4,
    question: 'Đâu không phải là một tác phẩm của họa sĩ Trần Văn Cẩn?',
    hint: '',
    answers: [
      {
        id: 1,
        text: 'Đôi bạn',
        isTrue: false,
      },
      {
        id: 2,
        text: 'Mẹ',
        isTrue: false,
      },
      {
        id: 3,
        text: 'Em gái tôi',
        isTrue: true,
      },
      {
        id: 4,
        text: 'Em Thuý',
        isTrue: false,
      },
    ],
  },
];

const initialState = {
  listQuestion: data,
  currentQuestion: data[0],
  questionDone: 0,
  totalQuestion: 5,
};

export const SakuSMReducer = createSlice({
  name: 'sakuSMReducer',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame(state) {
      state.listQuestion = data;
      state.currentQuestion = data[0];
      state.questionDone = 0;
      state.totalQuestion = data.length;
    },
    reset(state) {
      state.listQuestion = data;
      state.currentQuestion = data[0];
      state.questionDone = 0;
      state.totalQuestion = 5;
    },
  },
});

export const {setData, reset, startGame} = SakuSMReducer.actions;

export default SakuSMReducer.reducer;

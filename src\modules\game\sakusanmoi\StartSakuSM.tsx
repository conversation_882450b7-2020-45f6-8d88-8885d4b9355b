import {ImageBackground, StyleSheet, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import CountBadge from '../components/CountQuestions';
import {CardTitleGame} from '../components/CardTitleGame';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {BottomGame} from '../components/BottomGame';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSakuSMHook} from '../../../redux/hook/game/sakusmHook';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';
import ModelDoneLevel from '../components/ModelDoneLevel';
import {useEffect, useState} from 'react';
import CardAnswer from './components/CardAnswer';
import React from 'react';
import ModelPauseGame from '../components/ModelPauseGame';

const gemAdd = 30;
const gemCost = 10;
const cupAdd = 10;

const StartSakuSM = () => {
  const gameHook = useGameHook();
  const sakuSMHook = useSakuSMHook();
  const {currentQuestion, questionDone, totalQuestion, listQuestion} =
    useSelector((state: RootState) => state.SakuSMStore);
  const {isGameOver, messageGameOver, totalLives, currentLives, gem, cup} =
    useSelector((state: RootState) => state.gameStore);

  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);
  const [isWinLevel, setIsWinLevel] = useState<boolean>(false);
  const [isAllowChoose, setIsAllowChoose] = useState<boolean>(true);
  const [isPauseGame, setIsPauseGame] = useState<boolean>(false);

  useEffect(() => {
    onStartGame();
  }, []);
  useEffect(() => {
    if (currentLives < 1) {
      onGameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  const resetData = () => {
    setShowModelConfirm(false);
    setShowHintModel(false);
    setIsWinLevel(false);
    setIsAllowChoose(true);
  };

  const onStartGame = () => {
    resetData();
    sakuSMHook.startGame();
    gameHook.restartGame();
  };

  //Tạm dừng
  const onPauseGame = () => {
    setIsPauseGame(true);
    gameHook.pauseGame();
  };

  // Tiếp tục
  const onContinueGame = () => {
    setIsPauseGame(false);
    gameHook.continueGame();
  };

  // Thua game
  const onGameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // show model xác nhận dùng gợi ý
  const showModalConfirm = () => {
    setShowModelConfirm(true);
  };

  // chọn đáp án
  const onChooseAnswer = (status: 'correct' | 'wrong' | null) => {
    if (!status) return;
    setIsAllowChoose(false);
    if (status === 'wrong') {
      gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
    }
    setTimeout(() => {
      nextQuestion();
    }, 1000);
  };

  // Chuyển sang câu hỏi tiếp theo
  const nextQuestion = () => {
    sakuSMHook.setData({stateName: 'questionDone', value: questionDone + 1});

    if (questionDone + 1 >= totalQuestion) {
      setIsWinLevel(true);
      gameHook.setData({stateName: 'gem', value: gem + gemAdd});
      gameHook.setData({stateName: 'cup', value: cup + cupAdd});
      return;
    }
    sakuSMHook.setData({
      stateName: 'currentQuestion',
      value: listQuestion[questionDone + 1],
    });
    resetData();
  };

  // show model gợi ý
  const onUseHint = () => {
    if (gem < gemCost) return;
    gameHook.setData({stateName: 'gem', value: gem - gemCost});
    setShowHintModel(true);
  };

  return (
    <SafeAreaView style={{flex: 1}}>
      <ImageBackground
        source={require('./assets/background.png')}
        style={{flex: 1}}>
        <View style={{marginHorizontal: 16, flex: 1}}>
          {/* Header */}
          <View>
            <HeadGame
              timeOut={() => onGameOver('Hết giờ rồi, làm lại nào')}
              onUseHint={showModalConfirm}
              isShowSuggest={true}
            />
            <View>
              <LineProgressBar
                progress={
                  (questionDone / totalQuestion) * 100
                }></LineProgressBar>
              <View
                style={{
                  width: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <Lives
                  totalLives={totalLives}
                  currentLives={currentLives}></Lives>
                <CountBadge
                  current={questionDone}
                  total={totalQuestion}></CountBadge>
              </View>
            </View>
          </View>

          {/* Body */}
          <View style={{marginVertical: 16}}>
            <CardTitleGame
              title={currentQuestion.question || ''}></CardTitleGame>
            <View style={{marginTop: 100}}>
              {React.useMemo(() => {
                return currentQuestion.answers.map((item, index) => (
                  <View key={index}>
                    <CardAnswer
                      allowChoose={isAllowChoose}
                      answer={item}
                      index={index}
                      side={index % 2 === 0 ? 'left' : 'right'}
                      onClick={onChooseAnswer}
                    />
                  </View>
                ));
              }, [currentQuestion.answers])}
            </View>
          </View>

          {/* Bottom */}
          <View style={{position: 'absolute', bottom: 0, left: 0}}>
            <BottomGame
              resetGame={onStartGame}
              backGame={() => {}}
              pauseGame={onPauseGame}
              volumeGame={() => {}}
            />
          </View>
        </View>
      </ImageBackground>

      {/* Modal */}
      <View style={{zIndex: 1000}}>
        <ModelConfirm
          isShow={showModelConfirm}
          closeModal={() => setShowModelConfirm(false)}
          onConfirm={onUseHint}
        />
        <HintModel
          isShow={showHintModel}
          closeModal={() => setShowHintModel(false)}
          text={currentQuestion.hint}
        />
        <ModelPauseGame
          visible={isPauseGame}
          message={'Bạn đang tạm dừng trò chơi'}
          onContinue={onContinueGame}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={onStartGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <ModelDoneLevel
          visible={isWinLevel}
          onNextLevel={onStartGame}
          currentGem={gem - gemAdd}
          currentCup={cup - cupAdd}
          gemAdd={gemAdd}
          cupAdd={cupAdd}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({});

export default StartSakuSM;

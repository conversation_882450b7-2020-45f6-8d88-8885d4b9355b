import {useDispatch} from 'react-redux';
import {reset, setData, startGame} from '../../reducers/game/sakuSMReducer';

export const useSakuSMHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    reset: () => {
      dispatch(reset());
    },
  };

  return action;
};
